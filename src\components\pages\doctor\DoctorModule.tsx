'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { Doctor, CreateDoctorInput, viewDoctor, createDoctor, updateDoctor } from '@/src/api/doctor';
import toast from 'react-hot-toast';
import Loading from '../../layouts/loading';

interface DoctorModuleProps {
    mode?: 'add' | 'edit' | 'view';
    id?: string;
}

const DoctorModule: React.FC<DoctorModuleProps> = ({ mode = 'add', id }) => {
    const router = useRouter();
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [doctor, setDoctor] = useState<Doctor | null>(null);

    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
        reset,
    } = useForm<CreateDoctorInput>({
        defaultValues: {
            first_name: '',
            last_name: '',
            email: '',
            username: '',
        },
    });

    useEffect(() => {
        if ((mode === 'edit' || mode === 'view') && id) {
            setLoading(true);
            viewDoctor(Number(id))
                .then((d) => {
                    setDoctor(d);
                    if (mode === 'edit') {
                        setValue('first_name', d.first_name);
                        setValue('last_name', d.last_name);
                        setValue('email', d.email);
                        setValue('username', d.username);
                    }
                })
                .catch((err: any) => {
                    setError(err.message);
                    toast.error(err.message || 'Failed to load doctor details');
                    setTimeout(() => router.push('/doctor'), 2000);
                })
                .finally(() => setLoading(false));
        }
    }, [mode, id, router, setValue]);

    const onSubmit = async (data: CreateDoctorInput) => {
        setError(null);
        setLoading(true);

        try {
            if (mode === 'add') {
                await createDoctor(data);
            } else {
                await updateDoctor(Number(id), data);
            }
            toast.success(`Doctor ${mode === 'add' ? 'created' : 'updated'} successfully!`);
            router.push('/doctor');
        } catch (err: any) {
            const errorMessage = err?.response?.data?.message || err.message || 'An error occurred';
            toast.error(errorMessage);
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => router.push('/doctor');
    const handleEdit = () => router.push(`/doctor/${id}/?mode=edit`);
    const handleBack = () => router.push('/doctor');

    if (loading) {
        return <Loading />;
    }

    if (error && mode === 'view') {
        return (
            <div className="mx-auto bg-white p-6 rounded shadow-md">
                <div className="text-red-600 text-center">
                    <h2 className="text-xl font-bold mb-2">Error</h2>
                    <p>{error}</p>
                    <p>Redirecting to doctor list...</p>
                </div>
            </div>
        );
    }

    if (mode === 'view' && doctor) {
        return (
            <div className="mx-auto card">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">Doctor Details</h2>
                    <div className="flex-gap">
                        <button type="button" onClick={handleEdit} className="btn-primary">
                            Edit Doctor
                        </button>
                        <button type="button" onClick={handleBack} className="btn-secondary">
                            Back
                        </button>
                    </div>
                </div>
                <div className="grid-2">
                    <div>
                        <label className="form-label">First Name</label>
                        <div className="form-input bg-disabled">{doctor.first_name}</div>
                    </div>
                    <div>
                        <label className="form-label">Last Name</label>
                        <div className="form-input bg-disabled">{doctor.last_name}</div>
                    </div>
                    <div>
                        <label className="form-label">Email</label>
                        <div className="form-input bg-disabled">{doctor.email}</div>
                    </div>
                    <div>
                        <label className="form-label">Username</label>
                        <div className="form-input bg-disabled">{doctor.username}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="mx-auto card">
            <h2 className="text-2xl font-bold mb-6">{mode === 'add' ? 'Add Doctor' : 'Edit Doctor'}</h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid-2">
                    {/* First Name */}
                    <div>
                        <label className="form-label">First Name</label>
                        <Controller
                            name="first_name"
                            control={control}
                            rules={{ required: 'First name is required' }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    type="text"
                                    placeholder="Enter first name"
                                    className="form-input"
                                    disabled={loading}
                                />
                            )}
                        />
                        {errors.first_name && <p className="form-error">{errors.first_name.message}</p>}
                    </div>
                    {/* Last Name */}
                    <div>
                        <label className="form-label">Last Name</label>
                        <Controller
                            name="last_name"
                            control={control}
                            rules={{ required: 'Last name is required' }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    type="text"
                                    placeholder="Enter last name"
                                    className="form-input"
                                    disabled={loading}
                                />
                            )}
                        />
                        {errors.last_name && <p className="form-error">{errors.last_name.message}</p>}
                    </div>
                    {/* Email */}
                    <div>
                        <label className="form-label">Email</label>
                        <Controller
                            name="email"
                            control={control}
                            rules={{
                                required: 'Email is required',
                                pattern: {
                                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                    message: 'Invalid email address'
                                }
                            }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    type="email"
                                    placeholder="Enter email"
                                    className="form-input"
                                    disabled={loading}
                                />
                            )}
                        />
                        {errors.email && <p className="form-error">{errors.email.message}</p>}
                    </div>
                    {/* Username */}
                    <div>
                        <label className="form-label">Username</label>
                        <Controller
                            name="username"
                            control={control}
                            rules={{ required: 'Username is required' }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    type="text"
                                    placeholder="Enter username"
                                    className="form-input"
                                    disabled={loading}
                                    onChange={(e) => {
                                        // Remove spaces from username
                                        const value = e.target.value.replace(/\s/g, '');
                                        field.onChange(value);
                                    }}
                                />
                            )}
                        />
                        {errors.username && <p className="form-error">{errors.username.message}</p>}
                    </div>
                </div>
                <div className="flex-gap pt-4">
                    <button type="submit" disabled={loading} className="btn-primary">
                        {loading ? 'Saving...' : mode === 'add' ? 'Create Doctor' : 'Update Doctor'}
                    </button>
                    <button
                        type="button"
                        onClick={handleCancel}
                        disabled={loading}
                        className="btn-secondary"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default DoctorModule;
