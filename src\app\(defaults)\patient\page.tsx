'use client';

import React, { useState, useEffect, Fragment } from 'react';
import { EyeIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';
import { getAllPatients, Patient } from '@/src/api/patient';
import Loading from '@/src/components/layouts/loading';
import toast from 'react-hot-toast';

const PatientList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [patients, setPatients] = useState<Patient[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();

    useEffect(() => {
        const fetchPatients = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await getAllPatients();
                setPatients(data);
            } catch (err: any) {
                console.error('Error fetching patients:', err);
                setError(err.message || 'An error occurred while fetching patients');
                toast.error(err.message || 'Failed to load patients');
            } finally {
                setLoading(false);
            }
        };

        fetchPatients();
    }, []);

    const handleView = (id: number) => router.push(`/patient/${id}`);

    const filteredPatients = patients.filter(
        (p) =>
            `${p.first_name} ${p.last_name} ${p.email} ${p.dob} ${p.country}`
                .toLowerCase()
                .includes(search.toLowerCase())
    );

    const columns = [
        { name: 'First Name', selector: (row: Patient) => row.first_name, sortable: true },
        { name: 'Last Name', selector: (row: Patient) => row.last_name, sortable: true },
        { name: 'Email', selector: (row: Patient) => row.email, sortable: true },
        { name: 'DOB', selector: (row: Patient) => row.dob, sortable: true },
        { name: 'Country', selector: (row: Patient) => row.country, sortable: true },
        {
            name: 'Actions',
            cell: (row: Patient) => (
                <div className="flex gap-3 items-center">
                    <button onClick={() => handleView(row.id)} title="View">
                        <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                    </button>
                </div>
            ),
        },
    ];

    if (loading) {
        return <Loading />;
    }

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Patient List</h2>
            </div>

            <input
                type="text"
                placeholder="Search by name, email, phone, or address..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
            />

            <DataTable
                columns={columns}
                data={filteredPatients}
                pagination
                highlightOnHover
                responsive
                striped
                noHeader
            />
        </div>
    );
};

export default PatientList;