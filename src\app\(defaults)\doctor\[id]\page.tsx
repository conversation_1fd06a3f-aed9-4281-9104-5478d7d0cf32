'use client';

import DoctorModule from '@/src/components/pages/doctor/DoctorModule';

interface DoctorPageProps {
  params: {
    id: string;
  };
    searchParams: {
        mode?: 'edit' | 'view' | 'add';
    };
}

const DoctorPage = ({ params ,searchParams }: DoctorPageProps) => {
  const { id } = params;
  const mode = searchParams.mode;

  if (!id && mode !== 'add') {
    return <p>No doctor ID provided.</p>;
  }

  return <DoctorModule mode={mode} id={id} />;
};

export default DoctorPage;
