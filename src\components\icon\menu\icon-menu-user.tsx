import { FC } from "react";

interface IconMenuUserProps {
  className?: string;
}

const IconMenuUser: FC<IconMenuUserProps> = ({ className }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
  <path strokeLinecap="round" strokeLinejoin="round" d="M17 20v-2a4 4 0 0 0-4-4H7a4 4 0 0 0-4 4v2"/>
  <circle cx="9" cy="7" r="4" />
  <path strokeLinecap="round" strokeLinejoin="round" d="M23 20v-2a4 4 0 0 0-3-3.87"/>
  <path strokeLinecap="round" strokeLinejoin="round" d="M16 3.13a4 4 0 0 1 0 7.75"/>
</svg>

  );
};

export default IconMenuUser;