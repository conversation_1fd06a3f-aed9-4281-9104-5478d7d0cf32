'use client';
import PerfectScrollbar from 'react-perfect-scrollbar';
import { useDispatch, useSelector } from 'react-redux';
import Link from 'next/link';
import { toggleSidebar } from '@/src/store/themeConfigSlice';
import { IRootState } from '@/src/store';
import { useState, useEffect, useCallback } from 'react';
import IconCaretsDown from '@/src/components/icon/icon-carets-down';
import IconMenuDoctors from '@/src/components/icon/menu/icon-menu-doctor';
import IconMenuPlans from '@/src/components/icon/menu/icon-menu-plans';
import IconMenuDashboard  from '@/src/components/icon/menu/icon-menu-dashboard';
import IconMenuUser from '@/src/components/icon/menu/icon-menu-user';

import { usePathname } from 'next/navigation';
import { getTranslation } from '@/i18n';
import Image from 'next/image';

const Sidebar = () => {
    const dispatch = useDispatch();
    const { t } = getTranslation();
    const pathname = usePathname();
    const [currentMenu, setCurrentMenu] = useState<string>('');
    const [errorSubMenu, setErrorSubMenu] = useState(false);
    const [isMounted, setIsMounted] = useState(false);
    const themeConfig = useSelector((state: IRootState) => state.themeConfig);
    const semidark = useSelector((state: IRootState) => state.themeConfig.semidark);

    // Handle mount state
    useEffect(() => {
        setIsMounted(true);
    }, []);

    const toggleMenu = (value: string) => {
        setCurrentMenu((oldValue) => {
            return oldValue === value ? '' : value;
        });
    };

    useEffect(() => {
        if (!isMounted) return;

        const selector = document.querySelector('.sidebar ul a[href="' + pathname + '"]');
        if (selector) {
            selector.classList.add('active');
            const ul = selector.closest('ul.sub-menu');
            if (ul) {
                const ele = ul.closest('li.menu')?.querySelectorAll('.nav-link')[0] as HTMLElement;
                if (ele) {
                    setTimeout(() => {
                        ele.click();
                    });
                }
            }
        }
    }, [pathname, isMounted]);

    const setActiveRoute = useCallback(() => {
        if (!isMounted) return;
        const allLinks = document.querySelectorAll('.sidebar ul a.active');
        allLinks.forEach((element) => {
            element.classList.remove('active');
        });
        const selector = document.querySelector('.sidebar ul a[href="' + pathname + '"]');
        selector?.classList.add('active');
    }, [isMounted, pathname]);

    useEffect(() => {
        if (!isMounted) return;

        setActiveRoute();
        if (typeof window !== 'undefined' && window.innerWidth < 1024 && themeConfig.sidebar) {
            dispatch(toggleSidebar());
        }
    }, [pathname, isMounted, themeConfig.sidebar, dispatch, setActiveRoute]);

    // Don't render until mounted on client
    if (!isMounted) {
        return null;
    }

    return (
        <div className={semidark ? 'dark' : ''}>
            <nav className={`sidebar fixed bottom-0 top-0 z-50 h-full min-h-screen w-[260px]   transition-all duration-300 ${semidark ? 'text-white-dark' : ''}`}>
                <div className="h-full bg-white dark:bg-black">
                    <div className="flex items-center justify-between px-4 py-4">
                        <Link href="/" className="main-logo flex shrink-0 items-center">
                            <Image className="ml-[5px] w-[180px] flex-none" width={100} height={100} quality={100} src="/assets/images/logo.png" alt="logo" />
                        </Link>

                        <button
                        title='Close Sidebar'
                            type="button"
                            className="collapse-icon flex h-8 w-8 items-center rounded-full transition duration-300 hover:bg-gray-500/10 rtl:rotate-180 dark:text-white-light dark:hover:bg-dark-light/10"
                            onClick={() => dispatch(toggleSidebar())}
                        >
                            <IconCaretsDown className="m-auto rotate-90" />
                        </button>
                    </div>
                    <PerfectScrollbar className="relative h-[calc(100vh-80px)] py-4">
                        <ul className="relative space-y-0.5 p-4 py-0 font-semibold">
                            <li className="menu nav-item">
                                <Link href="/" className="group">
                                    <div className="flex items-center">
                                        <IconMenuDashboard className="shrink-0 group-hover:!text-primary" />
                                        <span className="text-black ltr:pl-3 rtl:pr-3 dark:text-[#506690] dark:group-hover:text-white-dark">{t('Dashboard')}</span>
                                    </div>
                                </Link>
                            </li>

                             <li className="menu nav-item">
                                <Link href="/doctor" className="group">
                                    <div className="flex items-center">
                                        <IconMenuDoctors className="shrink-0 group-hover:!text-primary" />
                                        <span className="text-black ltr:pl-3 rtl:pr-3 dark:text-[#506690] dark:group-hover:text-white-dark">{t('Doctors')}</span>
                                    </div>
                                </Link>
                            </li>

                            <li className="menu nav-item">
                                <Link href="/patient" className="group">
                                    <div className="flex items-center">
                                        <IconMenuUser className="shrink-0 group-hover:!text-primary" />
                                        <span className="text-black ltr:pl-3 rtl:pr-3 dark:text-[#506690] dark:group-hover:text-white-dark">{t('Patients')}</span>
                                    </div>
                                </Link>
                            </li>
                            <li className="menu nav-item">
                                <Link href="/plans" className="group">
                                    <div className="flex items-center">
                                        <IconMenuPlans className="shrink-0 group-hover:!text-primary" />
                                        <span className="text-black ltr:pl-3 rtl:pr-3 dark:text-[#506690] dark:group-hover:text-white-dark">{t('Plans')}</span>
                                    </div>
                                </Link>
                            </li>
                        </ul>
                    </PerfectScrollbar>
                </div>
            </nav>
        </div>
    );
};

export default Sidebar;
