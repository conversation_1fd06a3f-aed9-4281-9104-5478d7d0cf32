import apiClient from '@/src/utils/apiClient';
import { PLANS_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractPlansResponseArray } from '@/src/utils/apiErrorHandler';

export interface Plan {
    id: number;
    name: string;
    type: string;
    duration_years?: number | null;
    expiration_date: string | null;
    created_at: string;
    updated_at: string;
}

export interface CreatePlanInput {
    name: string;
    type: string;
    duration_years?: number | null;
    expiration_date?: string | null;
}

export interface UpdatePlanInput {
    name?: string;
    type?: string;
    duration_years?: number | null;
    expiration_date?: string | null;
}
// List plans
export const getAllPlans = async (): Promise<Plan[]> => {
    try {
        const response = await apiClient.get(PLANS_ENDPOINTS.GET_PLANS);
        return extractPlansResponseArray(response);
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load plans'));
    }
};



export const createPlan = async (planData: CreatePlanInput): Promise<Plan> => {
    try {
        const response = await apiClient.post(PLANS_ENDPOINTS.CREATE_PLAN, planData);
        return extractApiResponseData<Plan>(response, 'Unexpected response structure: data is not an object');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not create plan'));
    }
};



// Update plan
export const updatePlan = async (planId: number, planData: Partial<CreatePlanInput>): Promise<Plan> => {
    try {
        const response = await apiClient.patch(PLANS_ENDPOINTS.UPDATE_PLAN(planId.toString()), planData);
        return extractApiResponseData<Plan>(response, 'No data returned after update');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update plan'));
    }
};

// View one plan
export const viewPlan = async (planId: number): Promise<Plan> => {
    try {
        const response = await apiClient.get(PLANS_ENDPOINTS.GET_PLAN(planId.toString()));
        return extractApiResponseData<Plan>(response, 'No plan data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch plan'));
    }
};

// Delete plan
export const deletePlan = async (planId: number): Promise<void> => {
    try {
        await apiClient.delete(PLANS_ENDPOINTS.DELETE_PLAN(planId.toString()));
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not delete plan'));
    }
};
