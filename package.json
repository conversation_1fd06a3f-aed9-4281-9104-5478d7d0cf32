{"name": "orthondontic-labs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "dev:network": "node start-dev.js", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.10.6", "@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.1", "@fullcalendar/react": "^6.1.4", "@fullcalendar/timegrid": "^6.1.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@reduxjs/toolkit": "^2.2.7", "@tippyjs/react": "^4.2.6", "@types/node": "^22.4.0", "@types/react": "^18.0.27", "@types/react-dom": "^18.3.0", "@x1mrdonut1x/nouislider-react": "^3.4.3", "apexcharts": "^4.7.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "easymde": "^2.18.0", "eslint": "8.57.0", "eslint-config-next": "14.2.13", "formik": "^2.2.9", "highlight.js": "^11.7.0", "i18next": "^23.13.0", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "mantine-datatable": "^1.7.35", "next": "^14.2.30", "ni18n": "^1.0.5", "react": "^18.3.1", "react-animate-height": "^3.1.0", "react-apexcharts": "^1.7.0", "react-click-away-listener": "^2.2.2", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.4.1", "react-data-table-component": "^7.7.0", "react-dom": "^18.3.1", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.0.2", "react-images-uploading": "^3.1.7", "react-perfect-scrollbar": "^1.5.8", "react-popper": "^2.3.0", "react-quill": "^0.0.2", "react-redux": "^9.1.2", "react-router-dom": "^7.6.1", "react-select": "^5.7.0", "react-simplemde-editor": "^5.2.0", "react-sortablejs": "^6.1.4", "react-text-mask": "^5.5.0", "sortablejs": "^1.15.0", "sweetalert2": "^11.6.13", "sweetalert2-react-content": "^5.0.7", "swiper": "^11.1.14", "typescript": "^5.3.3", "universal-cookie": "^7.2.0", "yet-another-react-lightbox": "^3.15.6", "yup": "^1.4.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.8", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.10", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-flatpickr": "^3.8.8", "@types/react-redux": "^7.1.32", "@types/react-text-mask": "^5.4.11", "@types/sortablejs": "^1.15.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.1"}}