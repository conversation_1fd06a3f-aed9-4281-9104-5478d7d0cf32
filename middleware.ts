import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { validateToken } from './src/utils/tokenValidation'

// Skip patterns for public paths and files
const PUBLIC_FILE = /\.(.*)$/
const AUTH_PATHS = ['/login', '/forgot-password', '/reset-password']

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const token = request.cookies.get('accessAdminToken')?.value

  // Skip token validation for paths that don't need it
  const skipTokenValidation =
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    PUBLIC_FILE.test(pathname) ||
    AUTH_PATHS.includes(pathname);

  if (skipTokenValidation) {
    // For auth paths, check if user is already authenticated
    if (AUTH_PATHS.includes(pathname) && token && validateToken(token)) {
      // If user is authenticated and trying to access login page, redirect to dashboard
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // For all other skipped paths, just proceed
    return NextResponse.next()
  }

  // If no token exists, redirect to login
  if (!token) {
    const loginUrl = request.nextUrl.clone()
    loginUrl.pathname = '/login'
    return NextResponse.redirect(loginUrl)
  }

  // Validate the token - this now uses the cached validation
  if (!validateToken(token)) {
    // If token is invalid, clear cookies and redirect to login
    const response = NextResponse.redirect(new URL('/login', request.url))
    response.cookies.delete('accessAdminToken')
    response.cookies.delete('user')
    return response
  }

  return NextResponse.next()
}

export const config = {
  matcher: ['/:path*'],
}
