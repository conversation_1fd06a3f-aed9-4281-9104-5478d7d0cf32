'use client';

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  createPlan,
  updatePlan,
  viewPlan,
  Plan,
  CreatePlanInput,
  UpdatePlanInput,
} from '@/src/api/plans';
import toast from 'react-hot-toast';

interface PlansFormProps {
  planId?: number;
  mode?: 'view' | 'edit';
}

// helper to capitalize first letter for display
const capitalize = (s: string) =>
  s.length > 0 ? s.charAt(0).toUpperCase() + s.slice(1) : '';

const PlansForm: React.FC<PlansFormProps> = ({ planId, mode: propMode }) => {
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<Plan>({
    defaultValues: { name: '', type: '', duration_years: undefined },
  });

  const router = useRouter();
  const searchParams = useSearchParams();
  const mode = propMode || (searchParams.get('mode') as 'view' | 'edit' | null);
  const isViewMode = mode === 'view';

  const [loading, setLoading] = useState(false);
  const [plan, setPlan] = useState<Plan | null>(null);

  // watch type to conditionally show duration
  const selectedType = watch('type');

  // fetch existing plan in view/edit
  useEffect(() => {
    if (planId) {
      const fetchPlan = async () => {
        try {
          setLoading(true);
          const data = await viewPlan(planId);
          setPlan(data);
          setValue('name', data.name);
          setValue('type', data.type);
          setValue('duration_years', data.duration_years);
        } catch {
          toast.error('Failed to fetch plan details');
        } finally {
          setLoading(false);
        }
      };
      fetchPlan();
    }
  }, [planId, setValue]);

  const onSubmit = async (data: Plan) => {
    try {
      setLoading(true);
      if (planId) {
        const payload: UpdatePlanInput = {
          name: data.name as string,
          type: data.type as string,
          ...(data.type !== 'retainer' && { duration_years: data.duration_years }),
        };
        await updatePlan(planId, payload);
        toast.success('Plan updated successfully');
      } else {
        const payload: CreatePlanInput = {
          name: data.name as string,
          type: data.type as string,
          ...(data.type !== 'retainer' && { duration_years: data.duration_years }),
        };
        await createPlan(payload);
        toast.success('Plan created successfully');
      }
      router.push('/plans');
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // view-only mode
  if (isViewMode && plan) {
    return (
      <div className="mx-auto card">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Plan Details</h2>
          <div className="flex-gap">
            <button
              type="button"
              onClick={() => router.push(`/plans/${planId}?mode=edit`)}
              className="btn-primary"
            >
              Edit Plan
            </button>
            <button type="button" onClick={() => router.back()} className="btn-secondary">
              Back
            </button>
          </div>
        </div>
        <div className="grid-2">
          <div>
            <label className="form-label">Name</label>
            <div className="form-input bg-disabled">{plan.name}</div>
          </div>
          <div>
            <label className="form-label">Type</label>
            <div className="form-input bg-disabled">{capitalize(plan.type)}</div>
          </div>
          {plan.type !== 'retainer' && (
            <div>
              <label className="form-label">Duration (years)</label>
              <div className="form-input bg-disabled">{plan.duration_years}</div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // add/edit form
  return (
    <div className="mx-auto card">
      <h2 className="text-2xl font-bold mb-6">{planId ? 'Edit Plan' : 'Add Plan'}</h2>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid-2">
          <div>
            <label htmlFor="name" className="form-label">
              Name
            </label>
            <Controller
              name="name"
              control={control}
              rules={{ required: 'Name is required' }}
              render={({ field }) => (
                <input
                  {...field}
                  id="name"
                  placeholder="Enter plan name"
                  className="form-input"
                  disabled={isViewMode}
                />
              )}
            />
            {errors.name && <p className="form-error">{errors.name.message}</p>}
          </div>

          <div>
            <label htmlFor="type" className="form-label">
              Type
            </label>
            <Controller
              name="type"
              control={control}
              rules={{ required: 'Type is required' }}
              render={({ field }) => (
                <select
                  {...field}
                  id="type"
                  className="form-input"
                  disabled={isViewMode || mode === 'edit'}
                >
                  <option value="">Select type</option>
                  <option value="aligner">Aligner</option>
                  <option value="retainer">Retainer</option>
                </select>
              )}
            />
            {errors.type && <p className="form-error">{errors.type.message}</p>}
          </div>

          {selectedType !== 'retainer' && (
            <div>
              <label htmlFor="duration_years" className="form-label">
                Duration (years)
              </label>
              <Controller
                name="duration_years"
                control={control}
                rules={{
                  required: 'Duration is required',
                  pattern: { value: /^[0-9]*$/, message: 'Please enter a valid number' },
                  max: { value: 10, message: 'Duration cannot exceed 10 years' },
                }}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value ?? ''}
                    id="duration_years"
                    type="number"
                    placeholder="Enter duration in years"
                    className="form-input"
                    disabled={isViewMode}
                  />
                )}
              />
              {errors.duration_years && <p className="form-error">{errors.duration_years.message}</p>}
            </div>
          )}
        </div>

        <div className="flex-gap pt-4">
          <button type="submit" disabled={loading} className="btn-primary">
            {loading ? 'Saving...' : planId ? 'Update Plan' : 'Create Plan'}
          </button>
          <button type="button" onClick={() => router.back()} disabled={loading} className="btn-secondary">
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default PlansForm;
