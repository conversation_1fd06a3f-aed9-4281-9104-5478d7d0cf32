'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authService, User } from '@/src/api/auth';
import toast from 'react-hot-toast';
import Loading from '@/src/components/layouts/loading';

import { baseUrl } from '@/src/utils/apiRoutes';
import { useAuth } from '@/src/contexts/AuthContext';

const ProfilePage = () => {
    const router = useRouter();
    const { refreshUser, updateUser: contextUpdateUser } = useAuth();
    const [profile, setProfile] = useState<Partial<User>>({});
    const [loading, setLoading] = useState(true);
    const [imageFile, setImageFile] = useState<File | null>(null);
    useEffect(() => {
        const fetchProfile = async () => {
            try {
                const data = await authService.getProfile();
                setProfile(data);
            } catch (err: any) {
                toast.error(err.message || 'Failed to load profile');
            } finally {
                setLoading(false);
            }
        };
        fetchProfile();
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setProfile((prev) => ({ ...prev, [name]: value }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            // Show preview using a local object URL instead of FileReader
            const previewUrl = URL.createObjectURL(file);
            setProfile((prev) => ({ ...prev, profile_image: previewUrl }));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            const formData = new FormData();

            Object.entries(profile).forEach(([key, value]) => {
                if (key !== 'profile_image' && value) {
                    formData.append(key, value as string);
                }
            });

            if (imageFile) {
                formData.append('profile_image', imageFile);
            }

            formData.append('_method', 'PUT');
            const updatedUser = await authService.updateProfile(formData);
            toast.success('Profile updated successfully!');
            contextUpdateUser(updatedUser);
        } catch (err: any) {
            toast.error(err.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    if (loading) return <Loading />;

    const profileImageUrl = profile.profile_image?.startsWith('http') ? profile.profile_image : `${baseUrl}${profile.profile_image}`;

    return (
        <>
            <h2 className="text-2xl font-bold mb-4">Edit Profile</h2>
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="first_name" className="font-medium">
                            First Name:
                        </label>
                        <input id="first_name" name="first_name" value={profile.first_name || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                    </div>
                    <div>
                        <label htmlFor="last_name" className="font-medium">
                            Last Name:
                        </label>
                        <input id="last_name" name="last_name" value={profile.last_name || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                    </div>
                    <div>
                        <label htmlFor="email" className="font-medium">
                            Email:
                        </label>
                        <input id="email" name="email" type="email" value={profile.email || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                    </div>
                    <div className="col-span-1 md:col-span-2">
                        <label htmlFor="profile_image" className="font-medium">
                            Profile Image:
                        </label>
                        <div className="flex items-center gap-4 mt-1">
                            {profile.profile_image &&
                                (profile.profile_image.startsWith('blob:') ? (
                                    <img src={profile.profile_image} alt="Profile preview" className="h-24 w-24 rounded-full object-cover" />
                                ) : (
                                    <Image
                                        src={profile.profile_image.startsWith('http') ? profile.profile_image : `${baseUrl}${profile.profile_image}`}
                                        alt="Profile"
                                        width={96}
                                        height={96}
                                        className="h-24 w-24 rounded-full object-cover"
                                    />
                                ))}
                            <input id="profile_image" name="profile_image" type="file" onChange={handleFileChange} className="border p-2 w-full rounded-md" />
                        </div>
                    </div>
                </div>
                <div className="flex gap-4 mt-6">
                    <button type="submit" className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors duration-200" disabled={loading}>
                        {loading ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button type="button" onClick={() => router.back()} className="border border-gray-400 text-gray-700 px-6 py-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
                        Cancel
                    </button>
                </div>
            </form>
        </>
    );
};

export default ProfilePage;
