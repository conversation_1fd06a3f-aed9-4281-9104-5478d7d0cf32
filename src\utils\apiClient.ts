import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_BASE_URL } from '@/src/utils/apiRoutes';
import CryptoJs from 'crypto-js';
import { getCookies, clearAllCookies } from '@/src/utils/cookies';

// Encryption key for token storage
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || '7d7cd92a9c3055f30f8943b5092abb8e';

// Create the main API client
class APIClient {
    private client: AxiosInstance;

    constructor() {
        this.client = axios.create({
            baseURL: API_BASE_URL,
            timeout: 30000,
            withCredentials: true,
            headers: {
                'Content-Type': 'application/json',
            },
        });

        this.setupInterceptors();
    }

    private setupInterceptors() {
        // Request interceptor to add auth token
        this.client.interceptors.request.use(
            (config) => {
                // Get token from cookies if available
                if (typeof window !== 'undefined') {
                    const token = getCookies('accessAdminToken');
                    if (token && token.accessToken) {
                        config.headers.Authorization = `Bearer ${token.accessToken}`;
                    }
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // Response interceptor to handle auth errors
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    // Clear auth data and redirect to login
                    clearAllCookies();
                    if (typeof window !== 'undefined') {
                        window.location.href = '/login';
                    }
                }
                return Promise.reject(error);
            }
        );
    }

    // Generic request methods
    async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        return this.client.get<T>(url, config);
    }

    async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        return this.client.post<T>(url, data, config);
    }

    async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        return this.client.put<T>(url, data, config);
    }

    async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        return this.client.patch<T>(url, data, config);
    }

    async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        return this.client.delete<T>(url, config);
    }

    // Form data request (for login and other form-based endpoints)
    async postForm<T = any>(url: string, data: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        const formData = new URLSearchParams();

        // Convert object to form data
        Object.keys(data).forEach(key => {
            if (data[key] !== null && data[key] !== undefined) {
                formData.append(key, data[key]);
            }
        });

        return this.client.post<T>(url, formData, {
            ...config,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                ...config?.headers,
            },
        });
    }

    async putFormData<T = any>(url: string, data: Record<string, any>, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
        const formData = new FormData();
        Object.keys(data).forEach(key => {
            if (data[key] !== null && data[key] !== undefined) {
                formData.append(key, data[key]);
            }
        });

        return this.client.put<T>(url, formData, {
            ...config,
            headers: {
                'Content-Type': 'multipart/form-data',
                ...config?.headers,
            },
        });
    }

    // File upload method
    async uploadFile<T = any>(url: string, file: File, additionalData?: Record<string, any>): Promise<AxiosResponse<T>> {
        const formData = new FormData();
        formData.append('file', file);

        if (additionalData) {
            Object.keys(additionalData).forEach(key => {
                if (additionalData[key] !== null && additionalData[key] !== undefined) {
                    formData.append(key, additionalData[key]);
                }
            });
        }

        return this.client.post<T>(url, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    }

    // Get the underlying axios instance if needed
    getClient(): AxiosInstance {
        return this.client;
    }
}

// Create and export a singleton instance
const apiClient = new APIClient();
export default apiClient;

// Export the class for testing or custom instances
export { APIClient };
