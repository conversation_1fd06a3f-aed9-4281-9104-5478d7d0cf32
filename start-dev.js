const { exec } = require('child_process');
const os = require('os');

// Function to get local IP address
function getLocalIP() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            // Skip internal and non-IPv4 addresses
            if (interface.family === 'IPv4' && !interface.internal) {
                // Prefer addresses that start with 192.168 or 10. or 172.
                if (interface.address.startsWith('192.168') || 
                    interface.address.startsWith('10.') || 
                    interface.address.startsWith('172.')) {
                    return interface.address;
                }
            }
        }
    }
    return 'localhost';
}

const localIP = getLocalIP();
const port = process.env.PORT || 3000;

console.log('\n🚀 Starting Next.js Development Server...\n');
console.log('📱 Share these URLs with your friends on the same network:');
console.log(`   Local:   http://localhost:${port}`);
console.log(`   Network: http://${localIP}:${port}`);
console.log('\n' + '='.repeat(50) + '\n');

// Start Next.js dev server
exec('next dev -H 0.0.0.0', (error, stdout, stderr) => {
    if (error) {
        console.error(`Error: ${error}`);
        return;
    }
    console.log(stdout);
    if (stderr) {
        console.error(stderr);
    }
});
