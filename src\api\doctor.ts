import apiClient from '@/src/utils/apiClient';
import { DOCTOR_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractApiResponseArray } from '@/src/utils/apiErrorHandler';

export interface Doctor {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
    created_at: string;
    updated_at: string;
}

export interface CreateDoctorInput {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
}

// List doctors
export const getAllDoctors = async (): Promise<Doctor[]> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTORS);
        return extractApiResponseArray<Doctor>(response, 'Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load doctors'));
    }
};

// Create doctor
export const createDoctor = async (doctorData: CreateDoctorInput): Promise<Doctor> => {
    try {
        const response = await apiClient.post(DOCTOR_ENDPOINTS.CREATE_DOCTOR, doctorData);
        return extractApiResponseData<Doctor>(response, 'No data returned after creation');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not create doctor'));
    }
};

// Update doctor
export const updateDoctor = async (doctorId: number, doctorData: Partial<CreateDoctorInput>): Promise<Doctor> => {
    try {
        const response = await apiClient.patch(DOCTOR_ENDPOINTS.UPDATE_DOCTOR(doctorId.toString()), doctorData);
        return extractApiResponseData<Doctor>(response, 'No data returned after update');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update doctor'));
    }
};

// View one doctor
export const viewDoctor = async (doctorId: number): Promise<Doctor> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTOR(doctorId.toString()));
        return extractApiResponseData<Doctor>(response, 'No doctor data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch doctor'));
    }
};

// Delete doctor
export const deleteDoctor = async (doctorId: number): Promise<void> => {
    try {
        await apiClient.delete(DOCTOR_ENDPOINTS.DELETE_DOCTOR(doctorId.toString()));
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not delete doctor'));
    }
};
