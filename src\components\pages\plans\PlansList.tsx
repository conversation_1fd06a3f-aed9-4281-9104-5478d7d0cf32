'use client';

import React, { useState, useEffect, Fragment } from 'react';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';
import { Dialog, Transition } from '@headlessui/react';
import { getAllPlans, deletePlan } from '@/src/api/plans';
import { Plan } from '@/src/api/plans';
import toast from 'react-hot-toast';
import Loading from '../../layouts/loading';

const PlansList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [plans, setPlans] = useState<Plan[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [selectedId, setSelectedId] = useState<number | null>(null);
    const router = useRouter();

    // Fetch plans from API
    useEffect(() => {
        const fetchPlans = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await getAllPlans();
                setPlans(data);
            } catch (err: any) {
                setError(err.message || 'An error occurred while fetching plans');
            } finally {
                setLoading(false);
            }
        };
        fetchPlans();
    }, []);

    const confirmDelete = (id: number) => {
        setSelectedId(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (selectedId === null) return;

        try {
            await deletePlan(selectedId);
            setPlans((prev) => prev.filter((d) => d.id !== selectedId));
            toast.success('Plan deleted successfully');
        } catch (err: any) {
            console.error('Error deleting mainPlan:', err);
            toast.error(err.message || 'An error occurred while deleting the mainPlan');
        } finally {
            setShowModal(false);
            setSelectedId(null);
        }
    };

    const handleView = (id: number) => router.push(`/plans/${id}?mode=view`);
    const handleEdit = (id: number) => router.push(`/plans/${id}?mode=edit`);

    const filtered = plans.filter((d) => `${d.name}`.toLowerCase().includes(search.toLowerCase()));

    const columns = [
        { name: 'Name', selector: (r: Plan) => r.name, sortable: true },
        { name: 'Type', selector: (r: Plan) => r.type, sortable: true },
        { name: 'Duration (years)', selector: (r: Plan) => r.duration_years ?? '-', sortable: true },
        {
            name: 'Actions',
            cell: (row: Plan) => (
                <div className="flex gap-3 items-center">
                    <button onClick={() => handleView(row.id)} title="View">
                        <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                    </button>
                    <button onClick={() => handleEdit(row.id)} title="Edit">
                        <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
                    </button>
                    <button onClick={() => confirmDelete(row.id)} title="Delete">
                        <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
                    </button>
                </div>
            ),
        },
    ];

    // Show loading state
    if (loading) {
        return <Loading />;
    }

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Plans List</h2>
                <button
                    onClick={() => router.push('/plans/add')}
                    className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]"
                >
                    Add Plan
                </button>
            </div>

            <input
                type="text"
                placeholder="Search by name..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
            />

            <DataTable columns={columns} data={filtered} pagination highlightOnHover responsive striped noHeader />

            {/* Confirmation Modal with Headless UI */}
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-50" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                        Confirm Deletion
                                    </Dialog.Title>
                                    <div className="mt-2 text-gray-600 text-sm">Are you sure you want to delete this plan?</div>

                                    <div className="mt-4 flex justify-end gap-3">
                                        <button className="px-4 py-2 border rounded" onClick={() => setShowModal(false)}>
                                            Cancel
                                        </button>
                                        <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700" onClick={handleDelete}>
                                            Delete
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

export default PlansList;
